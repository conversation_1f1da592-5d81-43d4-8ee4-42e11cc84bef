# 板块显示规则文档

## 📋 概述

本文档详细说明TrendPulse系统前端板块数据的显示规则，包括精选板块列和普通板块列的显示逻辑、数据格式和表格结构规范。

## 🎯 表格整体结构

### 基本布局
- **表格尺寸**: 7行 × 8列
- **列构成**: 1个精选板块列 + 7个日期列
- **行构成**: 7行数据行
- **响应式设计**: 支持不同屏幕尺寸自适应

### 表头结构

```
┌─────────────────┬──────────────┬──────────────┬─────┬──────────────┐
│ 精选板块:日期 🌟 │ 日期:最新 🔄  │ 日期:历史1   │ ... │ 日期:历史6   │
├─────────────────┼──────────────┼──────────────┼─────┼──────────────┤
│ 板块名称|综合评分  │ 板块名称+评分 │ 板块名称+评分 │ ... │ 板块名称+评分 │
└─────────────────┴──────────────┴──────────────┴─────┴──────────────┘
```

## 🌟 精选板块列显示规则

### 数据来源
- **API端点**: `/api/selected-sectors`
- **数据表**: `selected_sectors`
- **数据范围**: 只显示最新交易日的数据

### 显示规则

#### 1. 数据筛选
```javascript
// 只保留最新交易日的数据
const latestDate = getLatestTradingDate(selectedData);
const filteredData = selectedData.filter(item => item.日期 === latestDate);
```

#### 2. 数据排序
- **排序字段**: `板块评分` (comprehensive_score)
- **排序方式**: 降序排列（评分高的在前）
- **最大显示**: 7条记录（对应7行）

#### 3. 显示格式
```
格式：板块名称 | 综合评分
示例：医药 | 11.3
```

#### 4. 空数据处理
- **显示内容**: `- | -`
- **触发条件**: 当精选板块数据少于7条时
- **样式**: 灰色斜体文字

### 列标题规则
```
格式：精选板块:YYYYMMDD 🌟
示例：精选板块:20250801 🌟
```

### 实际显示示例

```
┌─────────────────────┐
│ 精选板块:20250801 🌟 │
├─────────────────────┤
│ 板块名称 | 综合评分    │
├─────────────────────┤
│ 医药 | 11.3          │
│ 优化生育（三孩）| 4.6  │
│ - | -               │
│ - | -               │
│ - | -               │
│ - | -               │
│ - | -               │
└─────────────────────┘
```

## 📊 普通板块列显示规则

### 数据来源
- **API端点**: `/api/sectors`
- **数据表**: `sectors`
- **数据范围**: 最近7个交易日的历史数据

### 显示规则

#### 1. 日期范围确定
```javascript
// 获取最近7个交易日
const allDates = [...new Set(normalData.map(item => item.日期))];
allDates.sort((a, b) => b.localeCompare(a));
const dates = allDates.slice(0, 7);
```

#### 2. 数据分组
```javascript
// 按日期分组
const dataByDate = {};
dates.forEach(date => {
    dataByDate[date] = normalData
        .filter(item => item.日期 === date)
        .sort((a, b) => parseFloat(b.板块评分) - parseFloat(a.板块评分))
        .slice(0, 7); // 每天最多7条
});
```

#### 3. 显示格式
```
格式：板块名称
      板块评分
示例：医药
      13.7
```

#### 4. 空数据处理
- **显示内容**: 
  ```
  -
  -
  ```
- **触发条件**: 当某日期的数据少于7条时

### 列标题规则

#### 最新日期列
```
格式：日期:YYYYMMDD 🔄
示例：日期:20250801 🔄
```

#### 历史日期列
```
格式：日期:YYYYMMDD
示例：日期:20250731
```

### 实际显示示例

```
┌──────────────┬──────────────┬──────────────┐
│ 日期:20250801🔄│ 日期:20250731 │ 日期:20250730 │
├──────────────┼──────────────┼──────────────┤
│板块名称+板块评分│板块名称+板块评分│板块名称+板块评分│
├──────────────┼──────────────┼──────────────┤
│ 医药          │ 优化生育（三孩）│ 雅江电站概念股 │
│ 13.7         │ 11.7         │ 18.6         │
├──────────────┼──────────────┼──────────────┤
│ 人工智能大模型 │ 雅江电站概念股 │ 医药          │
│ 6.5          │ 9.8          │ 10.8         │
├──────────────┼──────────────┼──────────────┤
│ -            │ -            │ -            │
│ -            │ -            │ -            │
└──────────────┴──────────────┴──────────────┘
```

## 🎨 样式规范

### 表格样式
```css
.selected-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}
```

### 表头样式
```css
/* 精选板块列头 */
.selected-column-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

/* 最新日期列头 */
.latest-date-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

/* 普通日期列头 */
.normal-date-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
```

### 数据单元格样式
```css
.sector-cell {
    padding: 0.8rem;
    text-align: center;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
}

.sector-name {
    font-weight: 600;
    color: #333;
    cursor: pointer;
}

.sector-score {
    color: #e74c3c;
    font-weight: 600;
}

.empty-data {
    color: #999;
    font-style: italic;
}
```

## 🔄 数据更新机制

### 自动刷新
- **触发条件**: 页面加载完成
- **刷新频率**: 用户手动刷新或重新访问
- **数据获取**: 并行请求两个API

### 数据处理流程
```javascript
1. 并行获取 /api/sectors 和 /api/selected-sectors
2. 处理精选板块数据：筛选最新日期 + 排序
3. 处理普通板块数据：按日期分组 + 排序
4. 生成表格HTML
5. 初始化交互功能（高亮等）
```

## 🖱️ 交互功能

### 板块高亮
- **触发**: 鼠标悬停在板块名称上
- **效果**: 相同板块名称在整个表格中高亮显示
- **样式**: 黄色背景高亮

### 响应式适配
- **桌面端**: 完整显示所有列
- **平板端**: 可横向滚动
- **手机端**: 优先显示精选板块列和最新日期列

## 📏 数据限制规则

### 精选板块列
- **最大显示**: 7条记录
- **数据来源**: 仅最新交易日
- **排序**: 按综合评分降序

### 普通板块列
- **日期范围**: 最近7个交易日
- **每日最大**: 7条记录
- **排序**: 按板块评分降序

### 空数据填充
- **精选列**: 不足7条时用 `- | -` 填充
- **普通列**: 不足7条时用 `-\n-` 填充

## 🔍 数据验证

### 前端验证
1. **API响应状态检查**
2. **数据格式验证**
3. **必填字段检查**
4. **数值类型验证**

### 错误处理
- **API失败**: 显示错误提示
- **数据为空**: 显示空状态
- **格式错误**: 控制台警告

## 🔧 前端实现代码示例

### 数据加载和处理
```javascript
// 加载精选板块数据
async function loadSectorDataWithSelected() {
    try {
        console.log('🔄 开始加载精选板块数据...');

        // 获取两个API的数据
        const [sectorsResponse, selectedResponse] = await Promise.all([
            fetch('/api/sectors'),
            fetch('/api/selected-sectors')
        ]);

        const sectorsResult = await sectorsResponse.json();
        const selectedResult = await selectedResponse.json();

        if (sectorsResult.status === 'success' && selectedResult.status === 'success') {
            // 精选板块数据 - 只取最新交易日的数据
            const allSelectedData = selectedResult.data.map(item => ({
                日期: item.日期,
                板块名称: item.板块名称,
                板块评分: item.板块评分
            }));

            // 获取最新交易日期
            const allDates = [...new Set(allSelectedData.map(item => item.日期))];
            allDates.sort((a, b) => b.localeCompare(a));
            const latestDate = allDates[0];

            // 只保留最新交易日的精选板块数据
            const selectedData = allSelectedData
                .filter(item => item.日期 === latestDate)
                .sort((a, b) => parseFloat(b.板块评分) - parseFloat(a.板块评分));

            // 普通板块数据（来自sectors API）
            const normalData = sectorsResult.data.map(item => ({
                日期: item.日期,
                板块名称: item.板块名称,
                板块评分: item.板块评分
            }));

            // 生成表格
            generateAndDisplayTable(selectedData, normalData);

            // 初始化高亮功能
            initializeSectorHighlight();

            console.log('✅ 数据加载完成');
        } else {
            console.error('❌ API数据获取失败');
        }
    } catch (error) {
        console.error('❌ 加载数据时发生错误:', error);
    }
}
```

### 表格生成逻辑
```javascript
// 生成并显示表格
function generateAndDisplayTable(selectedData, normalData) {
    // 获取最近7天日期
    const allDates = [...new Set([...selectedData, ...normalData].map(item => item.日期))];
    allDates.sort((a, b) => b.localeCompare(a));
    const dates = allDates.slice(0, 7);

    // 按日期分组普通数据
    const dataByDate = {};
    dates.forEach(date => {
        dataByDate[date] = normalData.filter(item => item.日期 === date)
            .sort((a, b) => parseFloat(b.板块评分) - parseFloat(a.板块评分))
            .slice(0, 7); // 每天最多7条
    });

    // 生成表格HTML
    let html = '<table class="selected-table">';

    // 表头
    html += '<thead>';
    html += '<tr>';

    // 精选板块列头
    const latestSelectedDate = selectedData.length > 0 ? selectedData[0].日期 : '无数据';
    html += `<th class="selected-column-header">精选板块:${latestSelectedDate} 🌟</th>`;

    // 日期列头
    dates.forEach((date, index) => {
        const isLatest = index === 0;
        const headerClass = isLatest ? 'latest-date-header' : 'normal-date-header';
        const dateDisplay = `日期:${date}${isLatest ? ' 🔄' : ''}`;
        html += `<th class="${headerClass}">${dateDisplay}</th>`;
    });

    html += '</tr>';

    // 子列头
    html += '<tr>';
    html += '<th class="sub-header">板块名称 | 综合评分</th>';
    dates.forEach(() => {
        html += '<th class="sub-header">板块名称 + 板块评分</th>';
    });
    html += '</tr>';
    html += '</thead>';

    // 表体
    html += '<tbody>';

    // 7行数据
    for (let i = 0; i < 7; i++) {
        html += '<tr>';

        // 精选板块列
        if (i < selectedData.length) {
            const selected = selectedData[i];
            html += `<td class="sector-cell"><span class="sector-name">${selected.板块名称}</span> | <span class="sector-score">${parseFloat(selected.板块评分).toFixed(1)}</span></td>`;
        } else {
            html += '<td class="empty-data">- | -</td>';
        }

        // 普通数据列
        dates.forEach(date => {
            const dateData = dataByDate[date] || [];
            if (i < dateData.length) {
                const item = dateData[i];
                html += `<td class="sector-cell"><span class="sector-name">${item.板块名称}</span><br><span class="sector-score">${parseFloat(item.板块评分).toFixed(1)}</span></td>`;
            } else {
                html += '<td class="empty-data">-<br>-</td>';
            }
        });

        html += '</tr>';
    }

    html += '</tbody>';
    html += '</table>';

    // 插入到页面
    const container = document.getElementById('selectedTableContent');
    if (container) {
        container.innerHTML = html;
    }
}
```

### 高亮交互功能
```javascript
// 初始化板块高亮功能
function initializeSectorHighlight() {
    const tableCells = document.querySelectorAll(".selected-table td");

    tableCells.forEach(cell => {
        const sectorNameElement = cell.querySelector(".sector-name");
        if (sectorNameElement) {
            cell.addEventListener("mouseenter", function() {
                const sectorText = sectorNameElement.textContent.trim();
                highlightSameSectors(sectorText);
            });

            cell.addEventListener("mouseleave", function() {
                clearAllHighlights();
            });
        }
    });
}

// 高亮相同板块名称
function highlightSameSectors(sectorName) {
    const allSectorNames = document.querySelectorAll(".sector-name");

    allSectorNames.forEach(element => {
        if (element.textContent.trim() === sectorName) {
            element.classList.add("sector-highlight");
        }
    });
}

// 清除所有高亮
function clearAllHighlights() {
    const highlightedElements = document.querySelectorAll(".sector-highlight");
    highlightedElements.forEach(element => {
        element.classList.remove("sector-highlight");
    });
}
```

## 📱 响应式设计规范

### 移动端适配
```css
@media (max-width: 768px) {
    .selected-table {
        font-size: 0.8rem;
    }

    .selected-table th,
    .selected-table td {
        padding: 0.5rem 0.3rem;
    }

    /* 隐藏部分历史列 */
    .selected-table th:nth-child(n+6),
    .selected-table td:nth-child(n+6) {
        display: none;
    }
}

@media (max-width: 480px) {
    /* 只显示精选列和最新日期列 */
    .selected-table th:nth-child(n+3),
    .selected-table td:nth-child(n+3) {
        display: none;
    }
}
```

## 🔍 数据质量检查

### 前端数据验证
```javascript
function validateTableData(selectedData, normalData) {
    const issues = [];

    // 检查精选板块数据
    if (selectedData.length === 0) {
        issues.push('警告：精选板块数据为空');
    }

    if (selectedData.length > 7) {
        issues.push(`警告：精选板块数据过多 (${selectedData.length}条)`);
    }

    // 检查普通板块数据
    if (normalData.length === 0) {
        issues.push('警告：普通板块数据为空');
    }

    // 检查数据完整性
    selectedData.forEach((item, index) => {
        if (!item.板块名称 || !item.板块评分) {
            issues.push(`错误：精选板块第${index + 1}条数据不完整`);
        }
    });

    if (issues.length > 0) {
        console.warn('数据质量检查发现问题:', issues);
    }

    return issues;
}
```

---

**文档版本**: v1.0
**最后更新**: 2025-08-02
