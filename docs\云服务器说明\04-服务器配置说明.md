# 服务器配置说明文档

## 📋 概述

本文档详细说明TrendPulse系统云服务器的部署架构、配置要求、安装步骤和运维指南。

## 🏗️ 部署架构

### 技术栈
- **操作系统**: Ubuntu 20.04 LTS
- **Web服务器**: Nginx 1.18+
- **应用服务器**: Node.js 18+
- **数据库**: PostgreSQL 12+
- **进程管理**: PM2 或 systemd

### 架构图
```
Internet
    │
    ▼
┌─────────────┐
│   Nginx     │ :80/:443 (反向代理)
│ (反向代理)   │
└─────────────┘
    │
    ▼
┌─────────────┐
│  Node.js    │ :3000 (应用服务)
│ API Server  │
└─────────────┘
    │
    ▼
┌─────────────┐
│ PostgreSQL  │ :5432 (数据存储)
│  Database   │
└─────────────┘
```

## 🔧 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上 SSD
- **网络**: 10Mbps以上带宽

### 软件要求
- **Ubuntu**: 20.04 LTS 或更高版本
- **Node.js**: 18.x LTS
- **PostgreSQL**: 12.x 或更高版本
- **Nginx**: 1.18 或更高版本

## 📦 安装步骤

### 1. 系统更新
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git vim htop
```

### 2. 安装Node.js
```bash
# 安装Node.js 18.x LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示对应版本
```

### 3. 安装PostgreSQL
```bash
# 安装PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql << EOF
CREATE DATABASE tradefusion;
CREATE USER trendpulse WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE tradefusion TO trendpulse;
\q
EOF
```

### 4. 安装Nginx
```bash
# 安装Nginx
sudo apt install -y nginx

# 启动服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 验证安装
sudo systemctl status nginx
```

## 🗄️ 数据库配置

### 创建数据表
```sql
-- 连接数据库
sudo -u postgres psql -d tradefusion

-- 创建涨停表
CREATE TABLE sectors (
    id SERIAL PRIMARY KEY,
    date VARCHAR(8) NOT NULL,
    sector_name VARCHAR(100) NOT NULL,
    sector_score DECIMAL(10,2) NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date, sector_name)
);

-- 创建精选表
CREATE TABLE selected_sectors (
    date VARCHAR(8) NOT NULL,
    sector_name VARCHAR(100) NOT NULL,
    comprehensive_score DECIMAL(10,2) NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(date, sector_name)
);

-- 创建索引
CREATE INDEX idx_sectors_date ON sectors(date);
CREATE INDEX idx_sectors_updated_at ON sectors(updated_at);
CREATE INDEX idx_selected_sectors_date ON selected_sectors(date);

-- 退出
\q
```

### PostgreSQL配置优化
```bash
# 编辑配置文件
sudo vim /etc/postgresql/12/main/postgresql.conf

# 添加以下配置
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

## 🚀 应用部署

### 1. 部署应用代码
```bash
# 创建应用目录
sudo mkdir -p /opt/trendpulse
sudo chown $USER:$USER /opt/trendpulse

# 克隆或上传代码
cd /opt/trendpulse
# 这里假设代码已经上传到服务器

# 安装依赖
npm install --production
```

### 2. 环境配置
```bash
# 创建环境配置文件
cat > /opt/trendpulse/.env << EOF
NODE_ENV=production
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tradefusion
DB_USER=trendpulse
DB_PASSWORD=your_secure_password
EOF

# 设置文件权限
chmod 600 /opt/trendpulse/.env
```

### 3. 应用配置文件示例
```javascript
// config/database.js
module.exports = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'tradefusion',
    user: process.env.DB_USER || 'trendpulse',
    password: process.env.DB_PASSWORD,
    max: 20,
    min: 5,
    idleTimeoutMillis: 30000
};
```

## 🌐 Nginx配置

### 创建站点配置
```bash
# 创建配置文件
sudo vim /etc/nginx/sites-available/trendpulse

# 添加以下配置
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    
    # 静态文件
    location / {
        root /opt/trendpulse/public;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/api/health;
    }
    
    # 日志配置
    access_log /var/log/nginx/trendpulse_access.log;
    error_log /var/log/nginx/trendpulse_error.log;
}
```

### 启用站点
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/trendpulse /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

## 🔄 进程管理

### 使用PM2管理Node.js应用
```bash
# 全局安装PM2
sudo npm install -g pm2

# 创建PM2配置文件
cat > /opt/trendpulse/ecosystem.config.js << EOF
module.exports = {
    apps: [{
        name: 'trendpulse-api',
        script: './src/app.js',
        cwd: '/opt/trendpulse',
        instances: 2,
        exec_mode: 'cluster',
        env: {
            NODE_ENV: 'production',
            PORT: 3000
        },
        error_file: './logs/err.log',
        out_file: './logs/out.log',
        log_file: './logs/combined.log',
        time: true,
        max_memory_restart: '1G',
        restart_delay: 4000
    }]
};
EOF

# 创建日志目录
mkdir -p /opt/trendpulse/logs

# 启动应用
cd /opt/trendpulse
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 使用systemd管理（替代方案）
```bash
# 创建systemd服务文件
sudo vim /etc/systemd/system/trendpulse.service

# 添加以下内容
[Unit]
Description=TrendPulse API Server
After=network.target postgresql.service

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/opt/trendpulse
ExecStart=/usr/bin/node src/app.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target

# 启用服务
sudo systemctl daemon-reload
sudo systemctl enable trendpulse
sudo systemctl start trendpulse
```

## 📊 监控和日志

### 日志配置
```bash
# 创建日志轮转配置
sudo vim /etc/logrotate.d/trendpulse

# 添加以下内容
/opt/trendpulse/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 系统监控
```bash
# 安装监控工具
sudo apt install -y htop iotop nethogs

# 查看系统资源
htop                    # CPU和内存使用情况
sudo iotop             # 磁盘I/O
sudo nethogs           # 网络使用情况

# 查看应用状态
pm2 status             # PM2进程状态
pm2 logs               # 查看日志
pm2 monit              # 实时监控
```

## 🔒 安全配置

### 防火墙设置
```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 查看状态
sudo ufw status
```

### SSL证书配置（可选）
```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 维护操作

### 应用更新
```bash
# 停止应用
pm2 stop trendpulse-api

# 备份当前版本
cp -r /opt/trendpulse /opt/trendpulse.backup.$(date +%Y%m%d)

# 更新代码
cd /opt/trendpulse
git pull origin main  # 或上传新版本

# 安装新依赖
npm install --production

# 重启应用
pm2 restart trendpulse-api
```

### 数据库备份
```bash
# 创建备份脚本
cat > /opt/backup_db.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# 备份数据库
sudo -u postgres pg_dump tradefusion > $BACKUP_DIR/tradefusion_$DATE.sql

# 保留最近7天的备份
find $BACKUP_DIR -name "tradefusion_*.sql" -mtime +7 -delete
EOF

# 设置执行权限
chmod +x /opt/backup_db.sh

# 添加到定时任务
sudo crontab -e
# 添加以下行（每天凌晨2点备份）
0 2 * * * /opt/backup_db.sh
```

### 性能调优
```bash
# 查看数据库性能
sudo -u postgres psql -d tradefusion -c "
SELECT schemaname,tablename,attname,n_distinct,correlation 
FROM pg_stats 
WHERE tablename IN ('sectors', 'selected_sectors');
"

# 分析查询性能
sudo -u postgres psql -d tradefusion -c "
EXPLAIN ANALYZE SELECT * FROM sectors 
WHERE date = '20250801' 
ORDER BY sector_score DESC 
LIMIT 7;
"
```

---

**文档版本**: v1.0  
**最后更新**: 2025-08-02
