// TrendPulse 本地监控服务配置文件
require('dotenv').config();

const config = {
  // 数据库配置 - PostgreSQL
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'tradefusion',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'ymjatTUU520',
    checkInterval: parseInt(process.env.CHECK_INTERVAL) || 30000, // 30秒检查一次
    maxRetries: parseInt(process.env.MAX_RETRIES) || 3,
    timeout: parseInt(process.env.DB_TIMEOUT) || 10000
  },

  // 推送配置
  push: {
    enabled: process.env.PUSH_ENABLED !== 'false',
    endpoint: process.env.PUSH_ENDPOINT || 'http://localhost:8080/api/data-changes',
    timeout: parseInt(process.env.PUSH_TIMEOUT) || 15000,
    retryAttempts: parseInt(process.env.PUSH_RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(process.env.PUSH_RETRY_DELAY) || 1000
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: process.env.LOG_CONSOLE !== 'false',
    enableFile: process.env.LOG_FILE === 'true',
    filePath: process.env.LOG_FILE_PATH || './logs/monitor.log',
    maxFileSize: process.env.LOG_MAX_SIZE || '10MB',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
  }
};

// 配置验证函数
function validateConfig() {
  const errors = [];

  // 验证数据库连接参数
  if (!config.database.host) {
    errors.push('数据库主机地址不能为空');
  }
  if (!config.database.port || config.database.port < 1 || config.database.port > 65535) {
    errors.push('数据库端口无效');
  }
  if (!config.database.database) {
    errors.push('数据库名称不能为空');
  }
  if (!config.database.user) {
    errors.push('数据库用户名不能为空');
  }
  if (!config.database.password) {
    errors.push('数据库密码不能为空');
  }

  // 验证检查间隔
  if (config.database.checkInterval < 1000) {
    errors.push('检查间隔不能小于1秒');
  }

  // 验证推送端点
  if (config.push.enabled && !config.push.endpoint) {
    errors.push('推送已启用但端点为空');
  }

  return errors;
}

// 导出配置和验证函数
module.exports = {
  config,
  validateConfig
};
