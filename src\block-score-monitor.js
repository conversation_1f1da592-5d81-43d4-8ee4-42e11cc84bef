// 板块涨停表专用监控服务
const BlockScorePusher = require('./block-score-pusher');
const { config, validateConfig } = require('../config/config');
const { Logger, TimeUtils } = require('./utils');

/**
 * 板块涨停表专用监控服务
 * 专门监控板块涨停表的板块名称和板块评分数据
 */
class BlockScoreMonitor {
  constructor() {
    this.config = config;
    this.logger = new Logger(this.config.logging);
    this.pusher = null;
    this.isRunning = false;
    this.intervalId = null;
    this.stats = {
      startTime: 0,
      totalChecks: 0,
      totalPushes: 0,
      lastCheckTime: 0,
      lastPushTime: 0,
      errors: []
    };
  }

  /**
   * 启动监控服务
   */
  async start() {
    try {
      this.logger.info('🎯 === TrendPulse 板块涨停表监控服务启动 ===');
      
      // 验证配置
      await this.validateConfiguration();
      
      // 初始化推送器
      this.pusher = new BlockScorePusher(this.config, this.logger);

      // 初始化数据库连接池
      await this.pusher.initializeDatabase();
      this.logger.info('✅ 数据库连接池初始化完成');

      // 执行首次推送
      await this.executeInitialPush();
      
      // 启动定期监控
      this.startPeriodicMonitoring();
      
      this.isRunning = true;
      this.stats.startTime = TimeUtils.now();
      
      this.logger.info('✅ 板块涨停表监控服务启动成功', {
        数据库主机: `${this.config.database.host}:${this.config.database.port}/${this.config.database.database}`,
        检查间隔: this.config.database.checkInterval + 'ms',
        推送端点: this.config.push.endpoint,
        推送状态: this.config.push.enabled ? '启用' : '禁用'
      });
      
    } catch (error) {
      this.logger.error('❌ 监控服务启动失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 停止监控服务
   */
  async stop() {
    try {
      this.logger.info('🛑 正在停止板块涨停表监控服务...');
      
      this.isRunning = false;
      
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }

      // 关闭数据库连接池
      if (this.pusher) {
        await this.pusher.closeDatabase();
        this.logger.info('✅ 数据库连接池已关闭');
      }

      this.logger.info('✅ 板块涨停表监控服务已停止', {
        运行时长: this.getUptime(),
        总检查次数: this.stats.totalChecks,
        总推送次数: this.stats.totalPushes
      });
      
    } catch (error) {
      this.logger.error('❌ 停止监控服务时出错', { error: error.message });
    }
  }

  /**
   * 验证配置
   */
  async validateConfiguration() {
    this.logger.info('⚙️ 验证配置...');
    
    const errors = validateConfig();
    if (errors.length > 0) {
      throw new Error(`配置验证失败: ${errors.join(', ')}`);
    }
    
    this.logger.info('✅ 配置验证通过');
  }

  /**
   * 执行首次推送
   */
  async executeInitialPush() {
    try {
      this.logger.info('🚀 执行首次推送...');
      
      const result = await this.pusher.initialPush();
      
      if (result.success && !result.skipped) {
        this.stats.totalPushes++;
        this.stats.lastPushTime = TimeUtils.now();
        
        this.logger.info('✅ 首次推送完成', {
          推送记录数: result.recordsCount,
          覆盖天数: result.daysCount,
          响应状态: result.status
        });
      } else if (result.skipped) {
        this.logger.warn('⚠️ 首次推送跳过', { 原因: result.message });
      }
      
    } catch (error) {
      this.logger.error('❌ 首次推送失败', { error: error.message });
      this.recordError('首次推送失败', error);
      // 不抛出错误，继续启动监控
    }
  }

  /**
   * 启动定期监控
   */
  startPeriodicMonitoring() {
    this.logger.info(`⏰ 启动定期监控，检查间隔: ${this.config.database.checkInterval}ms`);
    
    // 设置定时检查
    this.intervalId = setInterval(() => {
      this.performPeriodicCheck();
    }, this.config.database.checkInterval);
  }

  /**
   * 执行定期检查
   */
  async performPeriodicCheck() {
    if (!this.isRunning) {
      return;
    }

    try {
      this.stats.totalChecks++;
      this.stats.lastCheckTime = TimeUtils.now();
      
      this.logger.debug(`🔍 执行第 ${this.stats.totalChecks} 次定期检查`);
      
      // 检测并推送变化
      const result = await this.pusher.detectAndPush();
      
      if (result.success && !result.skipped) {
        this.stats.totalPushes++;
        this.stats.lastPushTime = TimeUtils.now();
        
        this.logger.info('✅ 定期检查推送成功', {
          检查次数: this.stats.totalChecks,
          推送记录数: result.recordsCount,
          总推送次数: this.stats.totalPushes
        });
      } else if (result.skipped) {
        this.logger.debug('⏭️ 定期检查跳过推送', { 原因: result.message });
      }
      
    } catch (error) {
      this.logger.error('❌ 定期检查失败', { 
        error: error.message,
        检查次数: this.stats.totalChecks
      });
      this.recordError('定期检查失败', error);
    }
  }

  /**
   * 记录错误
   */
  recordError(type, error) {
    const errorRecord = {
      type: type,
      message: error.message,
      timestamp: TimeUtils.now(),
      timestampFormatted: TimeUtils.formatTimestamp(TimeUtils.now())
    };
    
    this.stats.errors.push(errorRecord);
    
    // 只保留最近的20个错误
    if (this.stats.errors.length > 20) {
      this.stats.errors = this.stats.errors.slice(-20);
    }
  }

  /**
   * 获取运行时长
   */
  getUptime() {
    if (this.stats.startTime === 0) {
      return '未启动';
    }
    
    const uptimeMs = TimeUtils.now() - this.stats.startTime;
    const seconds = Math.floor(uptimeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      服务状态: this.isRunning ? '运行中' : '已停止',
      监控目标: '板块涨停表',
      启动时间: this.stats.startTime ? TimeUtils.formatTimestamp(this.stats.startTime) : null,
      运行时长: this.getUptime(),
      统计信息: {
        总检查次数: this.stats.totalChecks,
        总推送次数: this.stats.totalPushes,
        最后检查时间: this.stats.lastCheckTime ? TimeUtils.formatTimestamp(this.stats.lastCheckTime) : null,
        最后推送时间: this.stats.lastPushTime ? TimeUtils.formatTimestamp(this.stats.lastPushTime) : null,
        错误数量: this.stats.errors.length
      },
      推送器状态: this.pusher ? this.pusher.getStatus() : null,
      配置信息: {
        数据库路径: this.config.database.path,
        检查间隔: this.config.database.checkInterval + 'ms',
        推送启用: this.config.push.enabled,
        推送端点: this.config.push.endpoint
      }
    };
  }

  /**
   * 获取最近的错误
   */
  getRecentErrors(limit = 5) {
    return this.stats.errors.slice(-limit);
  }
}

// 如果直接运行此文件，启动监控服务
if (require.main === module) {
  const monitor = new BlockScoreMonitor();
  
  // 处理进程信号
  process.on('SIGINT', async () => {
    console.log('\n收到停止信号，正在关闭板块涨停表监控服务...');
    await monitor.stop();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n收到终止信号，正在关闭板块涨停表监控服务...');
    await monitor.stop();
    process.exit(0);
  });
  
  // 启动服务
  monitor.start().catch(error => {
    console.error('启动失败:', error.message);
    process.exit(1);
  });
}

module.exports = BlockScoreMonitor;
