// 板块涨停表专用推送器
const axios = require('axios');
const crypto = require('crypto');
const { TimeUtils, Logger } = require('./utils');
const DatabasePool = require('./database/connection-pool');
const SectorDAO = require('./dao/sector.dao');

/**
 * 板块涨停表专用推送器
 * 负责推送最近7天的板块名称和板块评分数据
 */
class BlockScorePusher {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger || new Logger(config.logging);
    this.hasInitialPush = false; // 是否已完成首次推送
    this.lastPushedDate = null; // 最后推送的日期
    this.lastPushedData = new Map(); // 最后推送的数据缓存 (日期 -> 数据哈希)
    this.dbPool = null; // 数据库连接池
    this.sectorDAO = null; // 数据访问层
  }

  /**
   * 初始化数据库连接池
   */
  async initializeDatabase() {
    if (!this.dbPool) {
      this.dbPool = new DatabasePool(this.config.database, this.logger);
      await this.dbPool.initialize();
      this.logger.info('✅ BlockScorePusher 数据库连接池初始化完成');

      // 初始化数据访问层
      this.sectorDAO = new SectorDAO(this.dbPool, this.logger);
      this.logger.info('✅ BlockScorePusher 数据访问层初始化完成');
    }
  }

  /**
   * 关闭数据库连接池
   */
  async closeDatabase() {
    if (this.dbPool) {
      await this.dbPool.close();
      this.dbPool = null;
      this.sectorDAO = null;
      this.logger.info('✅ BlockScorePusher 数据库连接池已关闭');
    }
  }

  /**
   * 获取数据库连接池状态
   */
  getDatabaseStatus() {
    return {
      pool: this.dbPool ? this.dbPool.getPoolStatus() : { status: 'not_initialized' },
      dao: this.sectorDAO ? this.sectorDAO.getStats() : { status: 'not_initialized' }
    };
  }

  /**
   * 获取最近7天的板块涨停表数据
   * @returns {Promise<Array>} 最近7天的数据
   */
  async getRecentSevenDaysData() {
    if (!this.sectorDAO) {
      throw new Error('数据访问层未初始化，请先调用 initializeDatabase()');
    }

    try {
      return await this.sectorDAO.getRecentSectorData(7);
    } catch (error) {
      this.logger.error('获取最近7天数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取最新日期的数据
   * @returns {Promise<Array>} 最新日期的数据
   */
  async getLatestDateData() {
    if (!this.sectorDAO) {
      throw new Error('数据访问层未初始化，请先调用 initializeDatabase()');
    }

    try {
      return await this.sectorDAO.getLatestSectorData();
    } catch (error) {
      this.logger.error('获取最新日期数据失败:', error);
      throw error;
    }
  }



  /**
   * 获取最近7天的精选板块数据
   * @returns {Promise<Array>} 最近7天的精选数据
   */
  async getRecentSevenDaysSelectedData() {
    if (!this.sectorDAO) {
      throw new Error('数据访问层未初始化，请先调用 initializeDatabase()');
    }

    try {
      return await this.sectorDAO.getRecentSelectedSectorData(7);
    } catch (error) {
      this.logger.error('获取最近7天精选数据失败:', error);
      throw error;
    }
  }

  /**
   * 计算数据哈希值用于变化检测
   * @param {Array} data - 数据数组
   * @returns {string} 数据哈希值
   */
  calculateDataHash(data) {
    const dataString = JSON.stringify(data.map(item => ({
      date: item.日期,
      name: item.板块名称,
      score: item.板块评分
    })).sort((a, b) => a.name.localeCompare(b.name)));

    return require('crypto').createHash('md5').update(dataString).digest('hex');
  }

  /**
   * 首次启动推送最近7天数据
   * @returns {Promise<Object>} 推送结果
   */
  async initialPush() {
    try {
      this.logger.info('🚀 执行首次启动推送 - 最近7天板块数据（涨停表+精选表）');

      // 获取两个表的数据
      const recentData = await this.getRecentSevenDaysData();
      const selectedData = await this.getRecentSevenDaysSelectedData();

      if (recentData.length === 0 && selectedData.length === 0) {
        this.logger.warn('⚠️ 板块涨停表和精选表中都没有数据');
        return { success: true, message: '无数据需要推送', skipped: true };
      }

      // 按日期分组
      const groupedByDate = this.groupDataByDate(recentData);
      const selectedGroupedByDate = this.groupDataByDate(selectedData);
      const allDates = [...new Set([...Object.keys(groupedByDate), ...Object.keys(selectedGroupedByDate)])].sort((a, b) => b - a);

      this.logger.info(`📊 准备推送最近${allDates.length}天的板块数据`, {
        日期范围: allDates.length > 0 ? `${allDates[allDates.length - 1]} 至 ${allDates[0]}` : '无数据',
        涨停表记录数: recentData.length,
        精选表记录数: selectedData.length,
        总记录数: recentData.length + selectedData.length
      });

      // 构造推送数据
      const pushData = {
        source: "TrendPulse-Local",
        timestamp: TimeUtils.now(),
        timestampFormatted: TimeUtils.formatTimestamp(TimeUtils.now()),
        version: "1.0.0",
        type: "INITIAL_PUSH",
        changes: [
          {
            id: this.generateChangeId('initial_push_normal'),
            type: "INITIAL_DATA_PUSH",
            table: "板块涨停表",
            timestamp: TimeUtils.now(),
            timestampFormatted: TimeUtils.formatTimestamp(TimeUtils.now()),
            details: {
              tableInfo: [
                { name: "日期", type: "INTEGER" },
                { name: "板块名称", type: "TEXT" },
                { name: "板块评分", type: "REAL" }
              ],
              recentSevenDaysData: recentData.map(item => ({
                日期: item.日期,
                板块名称: item.板块名称,
                板块评分: item.板块评分,
                data_type: 'normal'
              })),
              dataByDate: groupedByDate,
              dateRange: {
                startDate: allDates.length > 0 ? allDates[allDates.length - 1] : null,
                endDate: allDates.length > 0 ? allDates[0] : null,
                totalDays: allDates.length
              }
            }
          },
          {
            id: this.generateChangeId('initial_push_selected'),
            type: "INITIAL_SELECTED_PUSH",
            table: "板块精选表",
            timestamp: TimeUtils.now(),
            timestampFormatted: TimeUtils.formatTimestamp(TimeUtils.now()),
            details: {
              tableInfo: [
                { name: "日期", type: "INTEGER" },
                { name: "板块名称", type: "TEXT" },
                { name: "综合评分", type: "REAL" }
              ],
              recentSevenDaysData: selectedData.map(item => ({
                日期: item.日期,
                板块名称: item.板块名称,
                综合评分: item.综合评分,
                data_type: 'selected'
              })),
              dataByDate: selectedGroupedByDate,
              dateRange: {
                startDate: allDates.length > 0 ? allDates[allDates.length - 1] : null,
                endDate: allDates.length > 0 ? allDates[0] : null,
                totalDays: allDates.length
              }
            }
          }
        ],
        summary: `首次推送板块数据：涨停表${recentData.length}条记录，精选表${selectedData.length}条记录，覆盖${allDates.length}天数据（${allDates.length > 0 ? allDates[allDates.length - 1] : ''}至${allDates.length > 0 ? allDates[0] : ''}）`
      };

      // 执行推送
      const result = await this.executePush(pushData);

      // 记录推送状态
      this.hasInitialPush = true;
      this.lastPushedDate = allDates.length > 0 ? allDates[0] : null; // 最新日期

      // 缓存所有推送的数据哈希
      allDates.forEach(date => {
        const dateData = groupedByDate[date];
        if (dateData) {
          const dataHash = this.calculateDataHash(dateData.map(item => ({
            日期: parseInt(date),
            板块名称: item.板块名称,
            板块评分: item.板块评分
          })));
          this.lastPushedData.set(date, dataHash);
        }
      });

      this.logger.info('✅ 首次推送成功', {
        推送记录数: recentData.length + selectedData.length,
        涨停表记录数: recentData.length,
        精选表记录数: selectedData.length,
        覆盖天数: allDates.length,
        响应状态: result.status
      });

      return {
        success: true,
        message: '首次推送成功',
        data: result.data,
        status: result.status,
        recordsCount: recentData.length + selectedData.length,
        daysCount: allDates.length
      };

    } catch (error) {
      this.logger.error('❌ 首次推送失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 检测板块涨停表变化并推送
   * @returns {Promise<Object>} 推送结果
   */
  async detectAndPush() {
    try {
      // 如果还没有进行首次推送，先执行首次推送
      if (!this.hasInitialPush) {
        return await this.initialPush();
      }

      // 智能变化检测逻辑：只检测最新日期的数据变化
      this.logger.debug('🔍 检测最新日期数据变化...');

      const latestData = await this.getLatestDateData();

      if (latestData.length === 0) {
        return { success: true, message: '无最新数据', skipped: true };
      }

      const latestDate = latestData[0].日期.toString();
      const currentDataHash = this.calculateDataHash(latestData);

      // {{ AURA-X: Modify - 恢复智能变化检测，避免过度推送导致服务器端异常. Approval: 寸止(ID:1722412000). }}
      const hasNewDate = latestDate !== this.lastPushedDate;
      const hasDataChange = this.lastPushedData.get(latestDate) !== currentDataHash;

      this.logger.debug('🔍 智能变化检测', {
        最新日期: latestDate,
        上次推送日期: this.lastPushedDate,
        有新日期: hasNewDate,
        数据有变化: hasDataChange,
        当前数据哈希: currentDataHash.substring(0, 8) + '...',
        推送策略: '仅在数据变化时推送'
      });

      // {{ AURA-X: Modify - 恢复智能变化检测，只在有数据变化时推送. Approval: 寸止(ID:1722480000). }}
      // 智能变化检测：只有在有新日期或数据变化时才推送
      if (!hasNewDate && !hasDataChange) {
        this.logger.info('⏭️ 数据无变化，跳过推送', {
          原因: '最新数据与上次推送数据相同',
          最新日期: latestDate,
          数据哈希: currentDataHash.substring(0, 8) + '...'
        });
        return { success: true, message: '最新数据无变化', skipped: true };
      }

      // 轮询模式：同时推送涨停表和精选表数据
      const changeType = hasNewDate ? 'NEW_DATE_DATA' : 'LATEST_DATE_UPDATED';

      // 获取精选板块数据
      const selectedData = await this.getRecentSevenDaysSelectedData();
      const latestSelectedData = selectedData.filter(item => item.日期.toString() === latestDate);

      const pushData = {
        source: "TrendPulse-Local",
        timestamp: TimeUtils.now(),
        timestampFormatted: TimeUtils.formatTimestamp(TimeUtils.now()),
        version: "1.0.0",
        type: "POLLING_UPDATE",
        changes: [
          {
            id: this.generateChangeId('polling_normal'),
            type: changeType,
            table: "板块涨停表",
            timestamp: TimeUtils.now(),
            timestampFormatted: TimeUtils.formatTimestamp(TimeUtils.now()),
            details: {
              tableInfo: [
                { name: "日期", type: "INTEGER" },
                { name: "板块名称", type: "TEXT" },
                { name: "板块评分", type: "REAL" }
              ],
              latestDateData: latestData.map(item => ({
                日期: item.日期,
                板块名称: item.板块名称,
                板块评分: item.板块评分,
                data_type: 'normal'
              })),
              changeInfo: {
                latestDate: latestDate,
                isNewDate: hasNewDate,
                isDataUpdated: hasDataChange,
                recordCount: latestData.length,
                dataHash: currentDataHash.substring(0, 8),
                pushMode: 'polling'
              }
            }
          },
          {
            id: this.generateChangeId('polling_selected'),
            type: "POLLING_SELECTED_UPDATE",
            table: "板块精选表",
            timestamp: TimeUtils.now(),
            timestampFormatted: TimeUtils.formatTimestamp(TimeUtils.now()),
            details: {
              tableInfo: [
                { name: "日期", type: "INTEGER" },
                { name: "板块名称", type: "TEXT" },
                { name: "综合评分", type: "REAL" }
              ],
              latestDateData: latestSelectedData.map(item => ({
                日期: item.日期,
                板块名称: item.板块名称,
                综合评分: item.综合评分,
                data_type: 'selected'
              })),
              changeInfo: {
                latestDate: latestDate,
                recordCount: latestSelectedData.length,
                pushMode: 'polling'
              }
            }
          }
        ],
        summary: `智能检测推送：${hasNewDate ? '新日期数据' : '数据变化'}，涨停表${latestData.length}条记录，精选表${latestSelectedData.length}条记录，总计${latestData.length + latestSelectedData.length}条记录`
      };

      const result = await this.executePush(pushData);

      // 更新推送状态
      this.lastPushedDate = latestDate;
      this.lastPushedData.set(latestDate, currentDataHash);

      this.logger.info('✅ 智能推送成功', {
        推送模式: '智能检测模式',
        推送原因: hasNewDate ? '检测到新日期数据' : '检测到数据变化',
        推送日期: latestDate,
        涨停表记录数: latestData.length,
        精选表记录数: latestSelectedData.length,
        总推送记录数: latestData.length + latestSelectedData.length,
        数据哈希: currentDataHash.substring(0, 8) + '...',
        变化类型: hasNewDate ? '新日期' : '数据更新'
      });

      return {
        success: true,
        message: '轮询推送成功',
        data: result.data,
        status: result.status,
        pushMode: 'polling',
        recordsCount: latestData.length + latestSelectedData.length,
        normalRecords: latestData.length,
        selectedRecords: latestSelectedData.length,
        latestDate: latestDate,
        changeType: hasNewDate ? 'new_date' : 'data_update',
        forceUpdate: !hasDataChange
      };

    } catch (error) {
      this.logger.error('❌ 变化检测推送失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 按日期分组数据
   * @param {Array} data - 原始数据
   * @returns {Object} 按日期分组的数据
   */
  groupDataByDate(data) {
    const grouped = {};
    data.forEach(row => {
      if (!grouped[row.日期]) {
        grouped[row.日期] = [];
      }
      grouped[row.日期].push({
        板块名称: row.板块名称,
        板块评分: row.板块评分
      });
    });
    return grouped;
  }

  /**
   * 生成变化ID
   * @param {string} type - 变化类型
   * @returns {string} 唯一ID
   */
  generateChangeId(type) {
    const data = `${type}-${TimeUtils.now()}`;
    return crypto.createHash('md5').update(data).digest('hex').substring(0, 8);
  }

  /**
   * 执行HTTP推送
   * @param {Object} data - 推送数据
   * @returns {Promise<Object>} HTTP响应
   */
  async executePush(data) {
    const axiosConfig = {
      method: 'POST',
      url: this.config.push.endpoint,
      data: data,
      timeout: this.config.push.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TrendPulse-BlockScore/1.0.0',
        'X-Source': 'TrendPulse-BlockScore-Monitor'
      }
    };

    if (this.config.push.apiKey) {
      axiosConfig.headers['Authorization'] = `Bearer ${this.config.push.apiKey}`;
    }

    return await axios(axiosConfig);
  }

  /**
   * 获取推送器状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      hasInitialPush: this.hasInitialPush,
      lastPushedDate: this.lastPushedDate,
      cachedDatesCount: this.lastPushedData.size,
      targetTable: "板块涨停表",
      pushEndpoint: this.config.push.endpoint,
      pushEnabled: this.config.push.enabled
    };
  }
}

module.exports = BlockScorePusher;
