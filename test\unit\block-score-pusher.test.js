// BlockScorePusher 单元测试
const BlockScorePusher = require('../../src/block-score-pusher');
const DatabasePool = require('../../src/database/connection-pool');
const SectorDAO = require('../../src/dao/sector.dao');

// Mock axios
jest.mock('axios');
const axios = require('axios');

// Mock DatabasePool
jest.mock('../../src/database/connection-pool');

// Mock SectorDAO
jest.mock('../../src/dao/sector.dao');

describe('BlockScorePusher', () => {
  let pusher;
  let mockConfig;
  let mockLogger;
  let mockDbPool;
  let mockSectorDAO;

  beforeEach(() => {
    jest.clearAllMocks();

    // 创建mock配置
    mockConfig = {
      database: {
        host: 'localhost',
        port: 5432,
        database: 'test_db',
        user: 'test_user',
        password: 'test_password'
      },
      push: {
        endpoint: 'http://test-server/api/data-changes',
        timeout: 30000
      },
      logging: {
        level: 'info'
      }
    };

    // 创建mock logger
    mockLogger = {
      info: jest.fn(),
      debug: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    };

    // 创建mock数据库连接池
    mockDbPool = {
      initialize: jest.fn(),
      close: jest.fn(),
      getPoolStatus: jest.fn().mockReturnValue({ status: 'connected' })
    };
    DatabasePool.mockImplementation(() => mockDbPool);

    // 创建mock SectorDAO
    mockSectorDAO = {
      getRecentSectorData: jest.fn(),
      getLatestSectorData: jest.fn(),
      getRecentSelectedSectorData: jest.fn(),
      getLatestSelectedSectorData: jest.fn(),
      getStats: jest.fn().mockReturnValue({ totalQueries: 0 })
    };
    SectorDAO.mockImplementation(() => mockSectorDAO);

    // 创建BlockScorePusher实例
    pusher = new BlockScorePusher(mockConfig, mockLogger);
  });

  describe('constructor', () => {
    test('应该正确初始化配置和状态', () => {
      expect(pusher.config).toEqual(mockConfig);
      expect(pusher.logger).toBe(mockLogger);
      expect(pusher.hasInitialPush).toBe(false);
      expect(pusher.lastPushedDate).toBeNull();
      expect(pusher.lastPushedData).toBeInstanceOf(Map);
      expect(pusher.dbPool).toBeNull();
      expect(pusher.sectorDAO).toBeNull();
    });
  });

  describe('initializeDatabase', () => {
    test('应该成功初始化数据库连接池和DAO', async () => {
      await pusher.initializeDatabase();

      expect(DatabasePool).toHaveBeenCalledWith(mockConfig.database, mockLogger);
      expect(mockDbPool.initialize).toHaveBeenCalled();
      expect(SectorDAO).toHaveBeenCalledWith(mockDbPool, mockLogger);
      expect(pusher.dbPool).toBe(mockDbPool);
      expect(pusher.sectorDAO).toBe(mockSectorDAO);
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('数据库连接池初始化完成')
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('数据访问层初始化完成')
      );
    });

    test('应该在重复初始化时跳过', async () => {
      await pusher.initializeDatabase();
      jest.clearAllMocks();

      await pusher.initializeDatabase();

      expect(DatabasePool).not.toHaveBeenCalled();
      expect(mockDbPool.initialize).not.toHaveBeenCalled();
    });
  });

  describe('closeDatabase', () => {
    test('应该成功关闭数据库连接池', async () => {
      await pusher.initializeDatabase();
      
      await pusher.closeDatabase();

      expect(mockDbPool.close).toHaveBeenCalled();
      expect(pusher.dbPool).toBeNull();
      expect(pusher.sectorDAO).toBeNull();
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('数据库连接池已关闭')
      );
    });

    test('应该在未初始化时安全关闭', async () => {
      await pusher.closeDatabase();

      expect(mockDbPool.close).not.toHaveBeenCalled();
    });
  });

  describe('getDatabaseStatus', () => {
    test('应该返回未初始化状态', () => {
      const status = pusher.getDatabaseStatus();
      
      expect(status.pool.status).toBe('not_initialized');
      expect(status.dao.status).toBe('not_initialized');
    });

    test('应该返回已初始化状态', async () => {
      await pusher.initializeDatabase();
      
      const status = pusher.getDatabaseStatus();
      
      expect(status.pool).toEqual({ status: 'connected' });
      expect(status.dao).toEqual({ totalQueries: 0 });
    });
  });

  describe('getRecentSevenDaysData', () => {
    beforeEach(async () => {
      await pusher.initializeDatabase();
    });

    test('应该返回最近7天的板块数据', async () => {
      const mockData = [
        { 日期: '20250730', 板块名称: '医药', 板块评分: 15.5 }
      ];
      mockSectorDAO.getRecentSectorData.mockResolvedValue(mockData);

      const result = await pusher.getRecentSevenDaysData();

      expect(result).toEqual(mockData);
      expect(mockSectorDAO.getRecentSectorData).toHaveBeenCalledWith(7);
    });

    test('应该在未初始化时抛出错误', async () => {
      const uninitializedPusher = new BlockScorePusher(mockConfig, mockLogger);
      
      await expect(uninitializedPusher.getRecentSevenDaysData()).rejects.toThrow(
        '数据访问层未初始化'
      );
    });

    test('应该在查询失败时抛出错误', async () => {
      const error = new Error('查询失败');
      mockSectorDAO.getRecentSectorData.mockRejectedValue(error);

      await expect(pusher.getRecentSevenDaysData()).rejects.toThrow('查询失败');
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('获取最近7天数据失败'),
        error
      );
    });
  });

  describe('getLatestDateData', () => {
    beforeEach(async () => {
      await pusher.initializeDatabase();
    });

    test('应该返回最新日期的板块数据', async () => {
      const mockData = [
        { 日期: '20250730', 板块名称: '医药', 板块评分: 15.5 }
      ];
      mockSectorDAO.getLatestSectorData.mockResolvedValue(mockData);

      const result = await pusher.getLatestDateData();

      expect(result).toEqual(mockData);
      expect(mockSectorDAO.getLatestSectorData).toHaveBeenCalled();
    });

    test('应该在未初始化时抛出错误', async () => {
      const uninitializedPusher = new BlockScorePusher(mockConfig, mockLogger);
      
      await expect(uninitializedPusher.getLatestDateData()).rejects.toThrow(
        '数据访问层未初始化'
      );
    });
  });

  describe('getRecentSevenDaysSelectedData', () => {
    beforeEach(async () => {
      await pusher.initializeDatabase();
    });

    test('应该返回最近7天的精选板块数据', async () => {
      const mockData = [
        { 日期: '20250730', 板块名称: '医药', 综合评分: 85.5 }
      ];
      mockSectorDAO.getRecentSelectedSectorData.mockResolvedValue(mockData);

      const result = await pusher.getRecentSevenDaysSelectedData();

      expect(result).toEqual(mockData);
      expect(mockSectorDAO.getRecentSelectedSectorData).toHaveBeenCalledWith(7);
    });

    test('应该在查询失败时抛出错误', async () => {
      const error = new Error('查询失败');
      mockSectorDAO.getRecentSelectedSectorData.mockRejectedValue(error);

      await expect(pusher.getRecentSevenDaysSelectedData()).rejects.toThrow('查询失败');
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('获取最近7天精选数据失败'),
        error
      );
    });
  });

  describe('calculateDataHash', () => {
    test('应该为相同数据生成相同哈希', () => {
      const data = [
        { 日期: '20250730', 板块名称: '医药', 板块评分: 15.5 }
      ];

      const hash1 = pusher.calculateDataHash(data);
      const hash2 = pusher.calculateDataHash(data);

      expect(hash1).toBe(hash2);
      expect(typeof hash1).toBe('string');
      expect(hash1.length).toBeGreaterThan(0);
    });

    test('应该为不同数据生成不同哈希', () => {
      const data1 = [{ 日期: '20250730', 板块名称: '医药', 板块评分: 15.5 }];
      const data2 = [{ 日期: '20250730', 板块名称: '科技', 板块评分: 12.3 }];

      const hash1 = pusher.calculateDataHash(data1);
      const hash2 = pusher.calculateDataHash(data2);

      expect(hash1).not.toBe(hash2);
    });
  });

  describe('executePush', () => {
    test('应该成功推送数据到服务器', async () => {
      const mockData = { test: 'data' };
      axios.mockResolvedValue({ status: 200, data: { success: true } });

      const result = await pusher.executePush(mockData);

      expect(result.status).toBe(200);
      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          data: mockData,
          timeout: expect.any(Number)
        })
      );
    });

    test('应该在推送失败时抛出错误', async () => {
      const mockData = { test: 'data' };
      const error = new Error('网络错误');
      axios.mockRejectedValue(error);

      await expect(pusher.executePush(mockData)).rejects.toThrow('网络错误');
    });
  });
});
