# TrendPulse系统技术文档

## 📋 文档概述

本文档集合包含TrendPulse板块分析系统的完整技术规范和部署说明。

## 📁 文档结构

```
docs/云服务器说明/
├── README.md                    # 文档索引（本文件）
├── 01-数据接收API规范.md         # API接口规范文档
├── 02-板块显示规则.md           # 前端显示规则文档
├── 03-数据流架构说明.md         # 系统架构文档
└── 04-服务器配置说明.md         # 部署配置文档
```

## 🎯 系统概述

TrendPulse是一个实时板块分析系统，包含以下核心组件：

### 核心组件
- **本地推送服务**：监控本地数据库变化，自动推送数据到云服务器
- **云服务器接收**：处理推送数据，存储到云端数据库
- **API服务**：提供数据查询接口
- **前端展示**：实时显示板块分析结果

### 数据流向
```
本地PostgreSQL → BlockScorePusher → 云服务器 → 数据库存储 → API → 前端显示
```

## 📖 快速导航

| 文档 | 描述 | 适用人员 |
|------|------|----------|
| [数据接收API规范](./01-数据接收API规范.md) | API接口详细规范 | 后端开发者 |
| [板块显示规则](./02-板块显示规则.md) | 前端显示逻辑规范 | 前端开发者 |
| [数据流架构说明](./03-数据流架构说明.md) | 系统整体架构 | 架构师、全栈开发者 |
| [服务器配置说明](./04-服务器配置说明.md) | 部署和运维指南 | 运维工程师 |

## 🔧 系统要求

### 本地环境
- Node.js 18+
- PostgreSQL 12+
- Windows/Linux/macOS

### 云服务器环境
- Ubuntu 20.04+
- Node.js 18+
- PostgreSQL 12+
- Nginx 1.18+

## 📞 技术支持

如有技术问题，请参考相应的技术文档或联系开发团队。

---

**文档版本**: v1.0  
**最后更新**: 2025-08-02  
**维护者**: TrendPulse开发团队
