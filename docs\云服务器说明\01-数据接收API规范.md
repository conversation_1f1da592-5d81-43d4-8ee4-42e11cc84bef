# 数据接收API规范文档

## 📋 概述

本文档详细说明TrendPulse系统中 `/api/data-changes` 接口的规范，包括请求格式、参数说明、响应规范和错误处理机制。

## 🔗 接口基本信息

- **接口路径**: `/api/data-changes`
- **请求方法**: `POST`
- **Content-Type**: `application/json`
- **功能**: 接收本地推送的板块数据并存储到云端数据库

## 📤 请求格式

### 支持的数据格式

接口支持两种数据格式：

1. **TrendPulse复杂格式**（推荐）：本地推送服务使用的标准格式
2. **简单数据格式**：用于测试和简单数据推送

## 🔄 TrendPulse复杂格式（changes数组）

### 请求结构

```json
{
  "changes": [
    {
      "table": "板块涨停表",
      "details": {
        "recentSevenDaysData": [
          {
            "日期": "20250801",
            "板块名称": "医药",
            "板块评分": 13.7
          }
        ],
        "latestDateData": [...]
      }
    },
    {
      "table": "板块精选表",
      "details": {
        "recentSevenDaysData": [
          {
            "日期": "20250801",
            "板块名称": "医药",
            "综合评分": 11.32
          }
        ]
      }
    }
  ],
  "metadata": {
    "source": "TrendPulse-Local",
    "timestamp": "2025-08-02T10:30:00Z",
    "version": "1.0"
  }
}
```

### 字段说明

#### changes数组
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| table | string | 是 | 数据表类型："板块涨停表" 或 "板块精选表" |
| details | object | 是 | 详细数据内容 |

#### details对象
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| recentSevenDaysData | array | 是 | 最近7天的数据数组 |
| latestDateData | array | 否 | 最新日期的数据数组 |

#### 涨停表数据项
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| 日期 | string | 是 | 格式：YYYYMMDD |
| 板块名称 | string | 是 | 板块名称 |
| 板块评分 | number | 是 | 板块评分 |

#### 精选表数据项
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| 日期 | string | 是 | 格式：YYYYMMDD |
| 板块名称 | string | 是 | 板块名称 |
| 综合评分 | number | 是 | 综合评分 |

## 📊 简单数据格式（data数组）

### 请求结构

```json
{
  "data": [
    {
      "date": "20250801",
      "sector_name": "医药",
      "comprehensive_score": 11.32,
      "table_type": "selected_sectors"
    },
    {
      "date": "20250801",
      "sector_name": "人工智能",
      "comprehensive_score": 8.5,
      "table_type": "sectors"
    }
  ],
  "metadata": {
    "source": "manual_test",
    "timestamp": "2025-08-02T10:30:00Z"
  }
}
```

### 字段说明

#### data数组项
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| date | string | 是 | 日期，格式：YYYYMMDD |
| sector_name | string | 是 | 板块名称 |
| comprehensive_score | number | 是 | 综合评分 |
| table_type | string | 否 | 数据类型："selected_sectors"(精选) 或 "sectors"(涨停) |

#### metadata对象
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| source | string | 否 | 数据来源标识 |
| timestamp | string | 否 | 时间戳，ISO 8601格式 |

## ✅ 响应格式

### 成功响应

```json
{
  "status": "success",
  "message": "数据接收成功",
  "processed": 65,
  "inserted": 45,
  "updated": 20,
  "received": 65,
  "timestamp": "2025-08-02T10:30:15.123Z"
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| status | string | 状态："success" 或 "error" |
| message | string | 处理结果描述 |
| processed | number | 实际处理的记录数 |
| inserted | number | 新插入的记录数 |
| updated | number | 更新的记录数 |
| received | number | 接收到的记录数 |
| timestamp | string | 处理完成时间戳 |

## ❌ 错误响应

### 错误格式

```json
{
  "status": "error",
  "message": "错误描述",
  "error": "详细错误信息",
  "code": "ERROR_CODE"
}
```

### 常见错误码

| HTTP状态码 | 错误码 | 说明 |
|------------|--------|------|
| 400 | INVALID_FORMAT | 数据格式错误 |
| 400 | MISSING_REQUIRED_FIELD | 缺少必填字段 |
| 500 | DATABASE_ERROR | 数据库操作失败 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

## 🔍 数据验证规则

### 基本验证
1. **请求体不能为空**
2. **必须包含 `changes` 数组或 `data` 数组**
3. **日期格式必须为 YYYYMMDD**
4. **评分必须为数字类型**

### 数据处理规则
1. **涨停表数据**：存储到 `sectors` 表
2. **精选表数据**：存储到 `selected_sectors` 表
3. **重复数据**：按 `(date, sector_name)` 去重，后来的数据覆盖先前的数据
4. **空数据**：跳过处理，不报错

## 🔄 字段映射关系

### TrendPulse格式 → 数据库字段

#### 涨停表映射
```
日期 → date
板块名称 → sector_name
板块评分 → sector_score
```

#### 精选表映射
```
日期 → date
板块名称 → sector_name
综合评分 → comprehensive_score
```

### 简单格式 → 数据库字段
```
date → date
sector_name → sector_name
comprehensive_score → comprehensive_score/sector_score
table_type → 决定存储到哪个表
```

## 📝 使用示例

### cURL示例

```bash
# TrendPulse复杂格式
curl -X POST http://your-server.com/api/data-changes \
  -H "Content-Type: application/json" \
  -d '{
    "changes": [
      {
        "table": "板块精选表",
        "details": {
          "recentSevenDaysData": [
            {
              "日期": "20250801",
              "板块名称": "医药",
              "综合评分": 11.32
            }
          ]
        }
      }
    ]
  }'

# 简单格式
curl -X POST http://your-server.com/api/data-changes \
  -H "Content-Type: application/json" \
  -d '{
    "data": [
      {
        "date": "20250801",
        "sector_name": "医药",
        "comprehensive_score": 11.32,
        "table_type": "selected_sectors"
      }
    ]
  }'
```

## 🔧 实际部署示例

### 云服务器接收处理逻辑

```javascript
app.post("/api/data-changes", async (req, res) => {
  try {
    console.log('📥 收到数据推送请求');
    console.log('📋 请求体结构:', Object.keys(req.body));

    let dataToProcess = [];

    // 处理TrendPulse本地推送的复杂格式
    if (req.body.changes && Array.isArray(req.body.changes)) {
      console.log('🔄 检测到TrendPulse本地推送格式');

      for (const change of req.body.changes) {
        if (change.details) {
          // 处理涨停表数据
          if (change.details.recentSevenDaysData && change.table === '板块涨停表') {
            dataToProcess.push(...change.details.recentSevenDaysData.map(item => ({
              date: item.日期?.toString() || item.date,
              sector_name: item.板块名称 || item.sector_name,
              comprehensive_score: item.板块评分 || item.comprehensive_score,
              table_type: 'sectors'
            })));
          }

          // 处理精选表数据
          if (change.details.recentSevenDaysData && change.table === '板块精选表') {
            dataToProcess.push(...change.details.recentSevenDaysData.map(item => ({
              date: item.日期?.toString() || item.date,
              sector_name: item.板块名称 || item.sector_name,
              comprehensive_score: item.综合评分 || item.comprehensive_score,
              table_type: 'selected_sectors'
            })));
          }
        }
      }

      console.log(`📊 从TrendPulse格式中提取到 ${dataToProcess.length} 条数据记录`);
    }
    // 处理简单格式
    else if (req.body.data && Array.isArray(req.body.data)) {
      console.log('🔄 检测到简单数据格式');
      dataToProcess = req.body.data;
    }

    if (dataToProcess.length === 0) {
      return res.status(400).json({
        status: "error",
        message: "数据格式错误：需要data数组或changes数组"
      });
    }

    console.log(`📊 接收到 ${dataToProcess.length} 条数据记录`);

    // 分离涨停表和精选表数据
    const sectorsData = dataToProcess.filter(item => item.table_type === 'sectors' || !item.table_type);
    const selectedData = dataToProcess.filter(item => item.table_type === 'selected_sectors');

    let insertedCount = 0;

    // 忽略涨停表数据（不存储到数据库）
    console.log(`🔍 涨停表数据: ${sectorsData.length}条（已忽略，不存储到数据库）`);

    // 只处理精选表数据
    if (selectedData.length > 0) {
      console.log(`📊 精选表数据: ${selectedData.length}条（将存储到数据库）`);
      for (const item of selectedData) {
        try {
          await db.query(`
            INSERT INTO selected_sectors (date, sector_name, comprehensive_score, updated_at)
            VALUES ($1, $2, $3, NOW())
            ON CONFLICT (date, sector_name)
            DO UPDATE SET
              comprehensive_score = EXCLUDED.comprehensive_score,
              updated_at = NOW()
          `, [item.date || item.日期, item.sector_name || item.板块名称, item.score || item.板块评分 || item.comprehensive_score]);
          insertedCount++;
        } catch (err) {
          console.error('插入精选板块数据失败:', err.message);
        }
      }
    } else {
      console.log('⚠️ 没有精选表数据需要处理');
    }

    res.json({
      status: "success",
      message: "数据接收成功",
      processed: insertedCount,
      received: dataToProcess.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 数据处理失败:', error);
    res.status(500).json({
      status: "error",
      message: "服务器内部错误",
      error: error.message
    });
  }
});
```

## 📋 数据处理规则总结

### 重要说明
1. **涨停表数据处理**: 当前版本中，涨停表数据被忽略，不存储到云端数据库
2. **精选表数据处理**: 只有精选表数据会被存储到 `selected_sectors` 表
3. **数据去重**: 使用 `(date, sector_name)` 作为唯一键，重复数据会被更新
4. **字段映射**: 支持中文字段名和英文字段名的自动映射

### API端点配置
- **精选板块查询**: `GET /api/selected-sectors`
- **涨停板块查询**: `GET /api/sectors` (查询 sectors 表)
- **数据推送**: `POST /api/data-changes`
- **健康检查**: `GET /api/health`

---

**文档版本**: v1.0
**最后更新**: 2025-08-02
