// TrendPulse 工具函数模块
const fs = require('fs');
const path = require('path');

/**
 * 日志工具类
 */
class Logger {
  constructor(config) {
    // 支持两种参数类型：配置对象或字符串名称
    if (typeof config === 'string') {
      // 如果传入字符串，使用默认配置
      this.config = {
        level: 'info',
        enableConsole: true,
        enableFile: false,
        filePath: './logs/monitor.log'
      };
      this.name = config;
    } else {
      // 如果传入配置对象，直接使用
      this.config = config || {
        level: 'info',
        enableConsole: true,
        enableFile: false,
        filePath: './logs/monitor.log'
      };
      this.name = 'Logger';
    }

    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
  }

  /**
   * 记录日志
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {object} meta - 额外信息
   */
  log(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...meta
    };

    // 控制台输出
    if (this.config.enableConsole && this.shouldLog(level)) {
      console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
      if (Object.keys(meta).length > 0) {
        console.log('详细信息:', meta);
      }
    }

    // 文件输出
    if (this.config.enableFile) {
      this.writeToFile(logEntry);
    }
  }

  /**
   * 判断是否应该记录该级别的日志
   */
  shouldLog(level) {
    return this.levels[level] <= this.levels[this.config.level];
  }

  /**
   * 写入日志文件
   */
  writeToFile(logEntry) {
    try {
      const logDir = path.dirname(this.config.filePath);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      const logLine = JSON.stringify(logEntry) + '\n';
      fs.appendFileSync(this.config.filePath, logLine);
    } catch (error) {
      console.error('写入日志文件失败:', error.message);
    }
  }

  // 便捷方法
  error(message, meta) { this.log('error', message, meta); }
  warn(message, meta) { this.log('warn', message, meta); }
  info(message, meta) { this.log('info', message, meta); }
  debug(message, meta) { this.log('debug', message, meta); }
}

/**
 * 时间工具
 */
class TimeUtils {
  /**
   * 获取当前时间戳（毫秒）
   */
  static now() {
    return Date.now();
  }

  /**
   * 格式化时间戳
   * @param {number} timestamp - 时间戳
   * @returns {string} 格式化的时间字符串
   */
  static formatTimestamp(timestamp) {
    return new Date(timestamp).toISOString();
  }

  /**
   * 计算时间差（毫秒）
   * @param {number} startTime - 开始时间
   * @param {number} endTime - 结束时间
   */
  static timeDiff(startTime, endTime = Date.now()) {
    return endTime - startTime;
  }
}

module.exports = {
  Logger,
  TimeUtils
};
