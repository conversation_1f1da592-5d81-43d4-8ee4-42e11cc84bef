# 数据流架构说明文档

## 📋 概述

本文档详细说明TrendPulse系统的整体架构设计、数据流向、组件交互和同步机制。

## 🏗️ 系统架构图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   本地环境       │    │    网络传输       │    │   云服务器环境   │
│                │    │                  │    │                │
│ ┌─────────────┐ │    │                  │    │ ┌─────────────┐ │
│ │PostgreSQL   │ │    │                  │    │ │PostgreSQL   │ │
│ │数据库       │ │    │                  │    │ │数据库       │ │
│ │- 板块涨停表  │ │    │                  │    │ │- sectors    │ │
│ │- 板块精选表  │ │    │                  │    │ │- selected_  │ │
│ └─────────────┘ │    │                  │    │ │  sectors    │ │
│        │        │    │                  │    │ └─────────────┘ │
│        ▼        │    │                  │    │        ▲        │
│ ┌─────────────┐ │    │  ┌─────────────┐ │    │ ┌─────────────┐ │
│ │BlockScore   │ │───────▶│HTTP POST    │──────▶│Node.js      │ │
│ │Pusher       │ │    │  │/api/data-   │ │    │ │API Server   │ │
│ │推送服务     │ │    │  │changes      │ │    │ │             │ │
│ └─────────────┘ │    │  └─────────────┘ │    │ └─────────────┘ │
│                │    │                  │    │        │        │
│                │    │                  │    │        ▼        │
│                │    │                  │    │ ┌─────────────┐ │
│                │    │                  │    │ │Nginx        │ │
│                │    │                  │    │ │反向代理     │ │
│                │    │                  │    │ └─────────────┘ │
│                │    │                  │    │        │        │
│                │    │                  │    │        ▼        │
│                │    │  ┌─────────────┐ │    │ ┌─────────────┐ │
│                │    │  │HTTP GET     │◀──────│前端静态页面  │ │
│                │    │  │/api/*       │ │    │ │HTML/CSS/JS  │ │
│                │    │  └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔄 数据流详细说明

### 1. 数据源（本地环境）

#### 数据库结构
```sql
-- 板块涨停表
CREATE TABLE "板块涨停表" (
    "日期" VARCHAR(8) NOT NULL,
    "板块名称" VARCHAR(100) NOT NULL,
    "板块评分" DECIMAL(10,2) NOT NULL,
    PRIMARY KEY ("日期", "板块名称")
);

-- 板块精选表
CREATE TABLE "板块精选表" (
    "日期" VARCHAR(8) NOT NULL,
    "板块名称" VARCHAR(100) NOT NULL,
    "综合评分" DECIMAL(10,2) NOT NULL,
    PRIMARY KEY ("日期", "板块名称")
);
```

#### 数据特征
- **涨停表**: 每日多条记录，记录当日表现突出的板块
- **精选表**: 每日少量记录（通常1-4条），记录精选的优质板块
- **更新频率**: 交易日结束后更新

### 2. 推送服务（BlockScorePusher）

#### 核心功能
```javascript
class BlockScorePusher {
    // 初始化推送
    async initialPush() {
        const recentData = await this.getRecentSevenDaysData();
        await this.pushToServer(recentData);
    }
    
    // 定期检查
    async periodicCheck() {
        const hasChanges = await this.detectChanges();
        if (hasChanges) {
            await this.pushChanges();
        }
    }
}
```

#### 智能检测机制
```javascript
// 数据变化检测
async detectChanges() {
    const currentHash = await this.calculateDataHash();
    const lastHash = this.getLastPushHash();
    return currentHash !== lastHash;
}
```

#### 推送策略
- **首次启动**: 推送最近7天的完整数据
- **定期检查**: 每30秒检查一次数据变化
- **智能推送**: 只在数据发生变化时推送
- **错误重试**: 推送失败时自动重试

### 3. 网络传输

#### 数据格式转换
```javascript
// 本地数据 → 推送格式
const pushData = {
    changes: [
        {
            table: "板块涨停表",
            details: {
                recentSevenDaysData: sectorsData
            }
        },
        {
            table: "板块精选表", 
            details: {
                recentSevenDaysData: selectedData
            }
        }
    ],
    metadata: {
        source: "TrendPulse-Local",
        timestamp: new Date().toISOString()
    }
};
```

#### 传输协议
- **协议**: HTTPS
- **方法**: POST
- **格式**: JSON
- **压缩**: gzip（可选）

### 4. 云服务器接收

#### 数据处理流程
```javascript
app.post("/api/data-changes", async (req, res) => {
    try {
        // 1. 格式识别
        const dataToProcess = await parseRequestData(req.body);
        
        // 2. 数据分类
        const sectorsData = filterSectorsData(dataToProcess);
        const selectedData = filterSelectedData(dataToProcess);
        
        // 3. 数据存储
        await storeSectorsData(sectorsData);      // → sectors表
        await storeSelectedData(selectedData);    // → selected_sectors表
        
        // 4. 响应确认
        res.json({ status: "success", processed: total });
    } catch (error) {
        res.status(500).json({ status: "error", error: error.message });
    }
});
```

### 5. 云端数据库

#### 表结构设计
```sql
-- 涨停表数据
CREATE TABLE sectors (
    id SERIAL PRIMARY KEY,
    date VARCHAR(8) NOT NULL,
    sector_name VARCHAR(100) NOT NULL,
    sector_score DECIMAL(10,2) NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date, sector_name)
);

-- 精选表数据
CREATE TABLE selected_sectors (
    date VARCHAR(8) NOT NULL,
    sector_name VARCHAR(100) NOT NULL,
    comprehensive_score DECIMAL(10,2) NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY(date, sector_name)
);
```

#### 数据去重策略
```sql
-- 使用 ON CONFLICT 处理重复数据
INSERT INTO selected_sectors (date, sector_name, comprehensive_score, updated_at)
VALUES ($1, $2, $3, NOW())
ON CONFLICT (date, sector_name) 
DO UPDATE SET 
    comprehensive_score = EXCLUDED.comprehensive_score,
    updated_at = NOW();
```

### 6. API服务层

#### 接口设计
```javascript
// 精选板块数据接口
app.get("/api/selected-sectors", async (req, res) => {
    const data = await db.query(`
        SELECT date, sector_name, comprehensive_score, updated_at
        FROM selected_sectors 
        ORDER BY date DESC, comprehensive_score DESC
    `);
    res.json({ status: "success", data: formatData(data) });
});

// 涨停板块数据接口
app.get("/api/sectors", async (req, res) => {
    const data = await db.query(`
        SELECT date, sector_name, sector_score as comprehensive_score, updated_at
        FROM sectors 
        ORDER BY date DESC, sector_score DESC
    `);
    res.json({ status: "success", data: formatData(data) });
});
```

### 7. 前端展示

#### 数据获取
```javascript
async function loadSectorDataWithSelected() {
    try {
        // 并行获取两个API的数据
        const [sectorsResponse, selectedResponse] = await Promise.all([
            fetch('/api/sectors'),
            fetch('/api/selected-sectors')
        ]);
        
        const sectorsResult = await sectorsResponse.json();
        const selectedResult = await selectedResponse.json();
        
        // 数据处理和显示
        processAndDisplayData(sectorsResult.data, selectedResult.data);
    } catch (error) {
        console.error('数据加载失败:', error);
    }
}
```

## ⚙️ 同步机制详解

### 数据一致性保证

#### 1. 时间戳机制
```javascript
// 每次推送都包含时间戳
const metadata = {
    timestamp: new Date().toISOString(),
    source: "TrendPulse-Local"
};
```

#### 2. 数据哈希校验
```javascript
// 计算数据哈希值
function calculateDataHash(data) {
    const dataString = JSON.stringify(data.sort());
    return crypto.createHash('md5').update(dataString).digest('hex');
}
```

#### 3. 冲突解决策略
- **时间优先**: 后推送的数据覆盖先前的数据
- **完整性检查**: 推送前验证数据完整性
- **事务处理**: 使用数据库事务确保原子性

### 错误处理机制

#### 1. 网络错误
```javascript
// 自动重试机制
async function pushWithRetry(data, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await pushToServer(data);
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            await sleep(1000 * Math.pow(2, i)); // 指数退避
        }
    }
}
```

#### 2. 数据错误
```javascript
// 数据验证
function validateData(data) {
    if (!data.日期 || !data.板块名称 || !data.板块评分) {
        throw new Error('数据格式错误');
    }
    if (!/^\d{8}$/.test(data.日期)) {
        throw new Error('日期格式错误');
    }
}
```

## 📊 性能优化

### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_sectors_date ON sectors(date);
CREATE INDEX idx_sectors_updated_at ON sectors(updated_at);
CREATE INDEX idx_selected_sectors_date ON selected_sectors(date);
```

### 缓存策略
```javascript
// API响应缓存
app.get("/api/sectors", cache('5 minutes'), async (req, res) => {
    // 缓存5分钟
});
```

### 连接池配置
```javascript
const pool = new Pool({
    host: 'localhost',
    database: 'tradefusion',
    user: 'postgres',
    max: 20,        // 最大连接数
    min: 5,         // 最小连接数
    idleTimeoutMillis: 30000
});
```

## 🔍 监控和日志

### 推送服务监控
```javascript
// 推送状态记录
const pushStats = {
    totalPushes: 0,
    successPushes: 0,
    failedPushes: 0,
    lastPushTime: null,
    lastError: null
};
```

### 服务器监控
```javascript
// API访问日志
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
    next();
});
```

---

**文档版本**: v1.0  
**最后更新**: 2025-08-02
